package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典分类
func (l *CreateDictCategoryLogic) CreateDictCategory(in *dict_category.CreateDictCategoryReq) (*dict_category.CreateDictCategoryResp, error) {
	// todo: add your logic here and delete this line
	
	return &dict_category.CreateDictCategoryResp{}, nil
}
