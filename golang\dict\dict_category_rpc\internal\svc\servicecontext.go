package svc

import (
	"dict_category_rpc/internal/config"
	"dict_category_rpc/model"
	"dict_rpc/dictservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config            config.Config
	DictCategoryModel model.DictCategoryModel
	DictRpc           dictservice.DictService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:            c,
		DictCategoryModel: *model.NewDictCategoryModel(model.NewDb(c.Mysql.DataSource)),
		DictRpc:           dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
	}
}
